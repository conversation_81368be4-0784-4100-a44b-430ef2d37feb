#!/usr/bin/env python3
"""
Production-ready dual LLM processor for OCR verification.
Processes documents through both Bedrock and Gemini LLMs with identical workflows.
"""

import os
import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple

# Set test environment to avoid SSM issues
os.environ['ENV'] = 'test'

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.services.process import process_text_pdf, process_scanned_pdf
from core.services.lambda_ocr_service import assess_pdf_quality_with_lambda_ocr
from core.llm.llm_factory import LlmFactory
from core.utils.utils import load_pdf_with_text
from core.logger.logger import get_logger, log_frame_info, log_frame_error, log_frame_warning
from core.enums.enums import ResponseStatus
from config.config import config

# Initialize logger
logger = get_logger(__name__)


class DualLLMProcessor:
    """
    Production-ready processor that handles PDF documents through both Bedrock and Gemini LLMs.
    Ensures identical processing workflows and validates consistency between providers.
    """
    
    def __init__(self):
        """Initialize the dual LLM processor."""
        self.bedrock_provider = None
        self.gemini_provider = None
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize both LLM providers."""
        try:
            # Clear any cached providers to ensure fresh instances
            LlmFactory.clear_cache()
            
            # Initialize Bedrock provider
            from core.llm.bedrock import BedrockLlm
            self.bedrock_provider = BedrockLlm
            log_frame_info(logger, "✅ Bedrock LLM provider initialized")
            
            # Initialize Gemini provider
            from core.llm.gemini import GeminiLlm
            self.gemini_provider = GeminiLlm
            log_frame_info(logger, "✅ Gemini LLM provider initialized")
            
        except Exception as e:
            log_frame_error(logger, f"Failed to initialize LLM providers: {str(e)}")
            raise
    
    def assess_document_quality(self, pdf_bytes: bytes) -> Dict[str, Any]:
        """
        Assess document quality using OCR service before LLM processing.
        
        Args:
            pdf_bytes: PDF document as bytes
            
        Returns:
            Quality assessment results
        """
        log_frame_info(logger, "🔍 Starting OCR quality assessment...")
        
        try:
            quality_result = assess_pdf_quality_with_lambda_ocr(pdf_bytes)
            
            if quality_result["status"] == ResponseStatus.SUCCESS.value:
                quality_data = quality_result["result"]
                confidence_score = quality_data["rapid_confidence"]["geometric_mean"]
                
                log_frame_info(logger, f"📊 OCR Quality Score: {confidence_score:.3f}")
                log_frame_info(logger, f"📋 Quality Assessment: {quality_data['quality_assessment']}")
                
                # Check against quality threshold
                threshold = float(config.ssm_config.get("document_quality_threshold", "0.7"))
                
                if confidence_score >= threshold:
                    log_frame_info(logger, f"✅ Document quality passed threshold ({threshold})")
                    return {
                        "status": "PASSED",
                        "confidence_score": confidence_score,
                        "threshold": threshold,
                        "quality_data": quality_data
                    }
                else:
                    log_frame_warning(logger, f"⚠️ Document quality below threshold ({threshold})")
                    return {
                        "status": "FAILED",
                        "confidence_score": confidence_score,
                        "threshold": threshold,
                        "quality_data": quality_data,
                        "message": f"Document quality score {confidence_score:.3f} is below threshold {threshold}"
                    }
            else:
                log_frame_error(logger, f"OCR quality assessment failed: {quality_result.get('message', 'Unknown error')}")
                return {
                    "status": "ERROR",
                    "message": quality_result.get("message", "OCR quality assessment failed")
                }
                
        except Exception as e:
            log_frame_error(logger, f"Error during quality assessment: {str(e)}")
            return {
                "status": "ERROR",
                "message": f"Quality assessment error: {str(e)}"
            }
    
    def process_with_provider(self, pdf_bytes: bytes, claim_payload: Dict[str, Any], 
                            provider_name: str, provider_class) -> Dict[str, Any]:
        """
        Process PDF with a specific LLM provider.
        
        Args:
            pdf_bytes: PDF document as bytes
            claim_payload: Claims payload for processing
            provider_name: Name of the provider (for logging)
            provider_class: LLM provider class
            
        Returns:
            Processing results
        """
        log_frame_info(logger, f"🚀 Processing with {provider_name} LLM...")
        
        try:
            start_time = time.time()
            
            # Temporarily override the LLM factory to use specific provider
            original_provider = LlmFactory._provider_instances.get(provider_name.lower())
            LlmFactory._provider_instances[provider_name.lower()] = provider_class
            
            # Set environment variable to ensure correct provider selection
            original_env = os.environ.get('LLM_PROVIDER')
            os.environ['LLM_PROVIDER'] = provider_name.lower()
            
            try:
                # Check if PDF has extractable text
                page_contents = load_pdf_with_text(pdf_bytes)
                
                if page_contents:
                    log_frame_info(logger, f"📄 Processing as text-based PDF with {provider_name}")
                    result = process_text_pdf(claim_payload, page_contents)
                else:
                    log_frame_info(logger, f"🖼️ Processing as scanned PDF with {provider_name}")
                    # Generate a unique request ID for this processing
                    request_id = f"dual_test_{provider_name.lower()}_{int(time.time())}"
                    result = process_scanned_pdf(request_id, pdf_bytes, claim_payload, retry=False)
                
                processing_time = time.time() - start_time
                result["processing_time"] = processing_time
                result["provider"] = provider_name
                
                log_frame_info(logger, f"✅ {provider_name} processing completed in {processing_time:.2f}s")
                return result
                
            finally:
                # Restore original environment and provider cache
                if original_env:
                    os.environ['LLM_PROVIDER'] = original_env
                elif 'LLM_PROVIDER' in os.environ:
                    del os.environ['LLM_PROVIDER']
                
                if original_provider:
                    LlmFactory._provider_instances[provider_name.lower()] = original_provider
                elif provider_name.lower() in LlmFactory._provider_instances:
                    del LlmFactory._provider_instances[provider_name.lower()]
                    
        except Exception as e:
            log_frame_error(logger, f"Error processing with {provider_name}: {str(e)}")
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"{provider_name} processing failed: {str(e)}",
                "provider": provider_name,
                "processing_time": time.time() - start_time if 'start_time' in locals() else 0
            }
    
    def compare_results(self, bedrock_result: Dict[str, Any], 
                       gemini_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compare results from both LLM providers to ensure consistency.
        
        Args:
            bedrock_result: Results from Bedrock processing
            gemini_result: Results from Gemini processing
            
        Returns:
            Comparison analysis
        """
        log_frame_info(logger, "🔍 Comparing results from both LLM providers...")
        
        comparison = {
            "status_match": bedrock_result.get("status") == gemini_result.get("status"),
            "bedrock_status": bedrock_result.get("status"),
            "gemini_status": gemini_result.get("status"),
            "differences": [],
            "similarities": [],
            "overall_consistency": True
        }
        
        # Compare extracted business rules if both succeeded
        if (bedrock_result.get("status") == ResponseStatus.SUCCESS.value and 
            gemini_result.get("status") == ResponseStatus.SUCCESS.value):
            
            bedrock_claims = bedrock_result.get("extracted_claims", {})
            gemini_claims = gemini_result.get("extracted_claims", {})
            
            # Compare each business rule field
            all_fields = set(bedrock_claims.keys()) | set(gemini_claims.keys())
            
            for field in all_fields:
                bedrock_value = bedrock_claims.get(field, "NOT_FOUND")
                gemini_value = gemini_claims.get(field, "NOT_FOUND")
                
                if bedrock_value == gemini_value:
                    comparison["similarities"].append({
                        "field": field,
                        "value": bedrock_value,
                        "match": True
                    })
                else:
                    comparison["differences"].append({
                        "field": field,
                        "bedrock_value": bedrock_value,
                        "gemini_value": gemini_value,
                        "match": False
                    })
                    comparison["overall_consistency"] = False
            
            # Compare final verification status
            bedrock_final_status = bedrock_result.get("status_message", "")
            gemini_final_status = gemini_result.get("status_message", "")
            
            if bedrock_final_status != gemini_final_status:
                comparison["differences"].append({
                    "field": "final_verification_status",
                    "bedrock_value": bedrock_final_status,
                    "gemini_value": gemini_final_status,
                    "match": False
                })
                comparison["overall_consistency"] = False
        
        # Log comparison results
        if comparison["overall_consistency"]:
            log_frame_info(logger, "✅ Both LLM providers produced consistent results")
        else:
            log_frame_warning(logger, f"⚠️ Found {len(comparison['differences'])} differences between providers")
            for diff in comparison["differences"]:
                log_frame_warning(logger, f"  - {diff['field']}: Bedrock='{diff['bedrock_value']}', Gemini='{diff['gemini_value']}'")
        
        return comparison

    def process_pdf_dual_llm(self, pdf_path: str, claim_payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main method to process PDF through both LLM providers with OCR quality assessment.

        Args:
            pdf_path: Path to the PDF file
            claim_payload: Claims payload for processing

        Returns:
            Complete processing results including comparison
        """
        log_frame_info(logger, f"🚀 Starting dual LLM processing for: {pdf_path}")

        try:
            # Load PDF file
            with open(pdf_path, 'rb') as f:
                pdf_bytes = f.read()

            log_frame_info(logger, f"📄 Loaded PDF: {len(pdf_bytes)} bytes")

            # Step 1: OCR Quality Assessment
            quality_assessment = self.assess_document_quality(pdf_bytes)

            if quality_assessment["status"] == "ERROR":
                return {
                    "status": ResponseStatus.FAIL.value,
                    "message": "OCR quality assessment failed",
                    "quality_assessment": quality_assessment
                }

            if quality_assessment["status"] == "FAILED":
                return {
                    "status": ResponseStatus.FAIL.value,
                    "message": "Document quality below threshold",
                    "quality_assessment": quality_assessment
                }

            # Step 2: Process with both LLM providers
            log_frame_info(logger, "🔄 Processing with both LLM providers...")

            bedrock_result = self.process_with_provider(
                pdf_bytes, claim_payload, "Bedrock", self.bedrock_provider
            )

            gemini_result = self.process_with_provider(
                pdf_bytes, claim_payload, "Gemini", self.gemini_provider
            )

            # Step 3: Compare results
            comparison = self.compare_results(bedrock_result, gemini_result)

            # Step 4: Generate final report
            final_result = {
                "status": ResponseStatus.SUCCESS.value,
                "message": "Dual LLM processing completed",
                "quality_assessment": quality_assessment,
                "bedrock_result": bedrock_result,
                "gemini_result": gemini_result,
                "comparison": comparison,
                "processing_summary": {
                    "bedrock_success": bedrock_result.get("status") == ResponseStatus.SUCCESS.value,
                    "gemini_success": gemini_result.get("status") == ResponseStatus.SUCCESS.value,
                    "results_consistent": comparison["overall_consistency"],
                    "bedrock_time": bedrock_result.get("processing_time", 0),
                    "gemini_time": gemini_result.get("processing_time", 0)
                }
            }

            # Log final summary
            log_frame_info(logger, "📊 DUAL LLM PROCESSING SUMMARY:")
            log_frame_info(logger, f"  📋 Quality Score: {quality_assessment['confidence_score']:.3f}")
            log_frame_info(logger, f"  🏗️ Bedrock Success: {final_result['processing_summary']['bedrock_success']}")
            log_frame_info(logger, f"  🤖 Gemini Success: {final_result['processing_summary']['gemini_success']}")
            log_frame_info(logger, f"  🔍 Results Consistent: {final_result['processing_summary']['results_consistent']}")
            log_frame_info(logger, f"  ⏱️ Bedrock Time: {final_result['processing_summary']['bedrock_time']:.2f}s")
            log_frame_info(logger, f"  ⏱️ Gemini Time: {final_result['processing_summary']['gemini_time']:.2f}s")

            return final_result

        except Exception as e:
            log_frame_error(logger, f"Dual LLM processing failed: {str(e)}")
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"Dual LLM processing failed: {str(e)}"
            }


def main():
    """Main function to test the dual LLM processor with the test PDF."""
    print("🚀 Starting Production Dual LLM OCR Verification Test")
    print("=" * 60)

    # Test configuration
    pdf_path = "tenancy-contract-01-bad.pdf"

    # Sample claim payload for testing (matching expected format)
    claim_payload = {
        "payload": {
            "tenant_name": "John Smith",
            "property_address": "123 Main Street",
            "monthly_rent": "2500",
            "lease_start_date": "2024-01-01",
            "lease_end_date": "2024-12-31"
        }
    }

    try:
        # Initialize processor
        processor = DualLLMProcessor()

        # Process PDF with both LLMs
        result = processor.process_pdf_dual_llm(pdf_path, claim_payload)

        # Display results
        print("\n" + "=" * 60)
        print("📊 FINAL RESULTS")
        print("=" * 60)

        if result["status"] == ResponseStatus.SUCCESS.value:
            print("✅ DUAL LLM PROCESSING SUCCESSFUL")

            summary = result["processing_summary"]
            print(f"\n📋 Processing Summary:")
            print(f"  Quality Score: {result['quality_assessment']['confidence_score']:.3f}")
            print(f"  Bedrock Success: {'✅' if summary['bedrock_success'] else '❌'}")
            print(f"  Gemini Success: {'✅' if summary['gemini_success'] else '❌'}")
            print(f"  Results Consistent: {'✅' if summary['results_consistent'] else '❌'}")
            print(f"  Bedrock Time: {summary['bedrock_time']:.2f}s")
            print(f"  Gemini Time: {summary['gemini_time']:.2f}s")

            if not summary['results_consistent']:
                print(f"\n⚠️ Found {len(result['comparison']['differences'])} differences:")
                for diff in result['comparison']['differences']:
                    print(f"  - {diff['field']}: Bedrock='{diff['bedrock_value']}', Gemini='{diff['gemini_value']}'")

            # Save detailed results to file
            with open("dual_llm_results.json", "w") as f:
                json.dump(result, f, indent=2, default=str)
            print(f"\n💾 Detailed results saved to: dual_llm_results.json")

        else:
            print("❌ DUAL LLM PROCESSING FAILED")
            print(f"Error: {result.get('message', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
