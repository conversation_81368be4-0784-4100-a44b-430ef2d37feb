# Local LLM Testing Guide

## Overview
The `local_llm_test.py` script allows you to test both Gemini and Bedrock LLM providers locally using the PDF documents in your repository.

## Prerequisites

### 1. Environment Setup
```bash
# Set environment to test mode (already done in script)
export ENV=test

# Ensure you're in the correct directory
cd app/lambdas/ocr_verification
```

### 2. API Keys (Required for actual testing)
- **Gemini**: Set `GEMINI_API_KEY` environment variable or update test config
- **Bedrock**: Ensure AWS credentials are configured (AWS CLI, IAM role, etc.)

### 3. Dependencies
All required dependencies should be installed via:
```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage
```bash
python local_llm_test.py
```

### Interactive Menu Options

1. **Test Bedrock only** - Tests Amazon Bedrock (Claude) with the PDF
2. **Test Gemini only** - Tests Google Gemini with the PDF  
3. **Test both providers** - Runs both and compares results
4. **Test OCR quality** - Assesses PDF quality using OCR service
5. **Custom business rules** - Test with your own extraction rules
6. **Save results** - Export test results to JSON file
7. **Exit** - Quit the application

## Test PDF
The script uses `tenancy-contract-01-bad.pdf` from your repository. The script will:
- Automatically detect if it's text-based or scanned
- Choose appropriate processing method
- Extract business rules like policy numbers, names, dates, etc.

## Sample Output

### PDF Analysis
```
📄 PDF Analysis:
  Type: scanned
  Pages: 3
  Image Sizes: ['2480x3508', '2480x3508', '2480x3508']
```

### Provider Results
```
============================================================
🤖 BEDROCK RESULTS
============================================================
Status: SUCCESS
Processing Time: 4.23s
Test Type: scanned

📋 Extracted Claims (7 fields):
  • policy_number: POL-2024-001
  • insured_name: John Smith
  • claim_number: CLM-2024-456
  • incident_date: 2024-01-15
  • claim_amount: $5,000
  • property_address: 123 Main St
  • damage_description: Water damage to kitchen

📊 Token Usage:
  • Input: 1250
  • Output: 180
  • Total: 1430
```

### Comparison Results
```
============================================================
🔍 PROVIDER COMPARISON
============================================================
Bedrock Success: ✅
Gemini Success: ✅
Results Consistent: ✅
Bedrock Time: 4.23s
Gemini Time: 3.87s

✅ Matching Fields (6):
  • policy_number: POL-2024-001
  • insured_name: John Smith
  • claim_number: CLM-2024-456
  • incident_date: 2024-01-15
  • property_address: 123 Main St
  • damage_description: Water damage to kitchen

⚠️ Different Fields (1):
  • claim_amount:
    - Bedrock: $5,000
    - Gemini: 5000.00
```

## Features

### Automatic PDF Type Detection
- **Text-based PDFs**: Uses direct text extraction
- **Scanned PDFs**: Converts to images and uses OCR + LLM processing

### Error Handling
- Graceful handling of API failures
- Detailed error messages and logging
- Fallback mechanisms

### Result Export
- Save test results to timestamped JSON files
- Include all metrics, timings, and extracted data
- Easy to analyze and compare later

### Comprehensive Logging
- Real-time progress updates
- Detailed error information
- Performance metrics

## Troubleshooting

### Common Issues

1. **"No Gemini API key found"**
   - Set environment variable: `export GEMINI_API_KEY=your_key_here`
   - Or update the test configuration

2. **"AWS credentials not found"**
   - Configure AWS CLI: `aws configure`
   - Or set AWS environment variables

3. **"PDF not found"**
   - Ensure you're running from the correct directory
   - Check that `tenancy-contract-01-bad.pdf` exists

4. **Import errors**
   - Install dependencies: `pip install -r requirements.txt`
   - Check Python path and virtual environment

### Debug Mode
For more detailed logging, you can modify the script to set log level to DEBUG:
```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## Customization

### Using Different PDFs
Modify the `pdf_path` in the `LocalLLMTester.__init__()` method:
```python
self.pdf_path = "your_custom_pdf.pdf"
```

### Custom Business Rules
Use option 5 in the menu to test with your own extraction rules:
```
Enter business rules (comma-separated):
policy_number,claim_date,damage_type,repair_cost
```

### Adding New Test Types
Extend the `test_single_provider()` method to support additional test scenarios.

## Performance Notes
- **Bedrock**: Typically 3-6 seconds for document processing
- **Gemini**: Typically 2-5 seconds for document processing  
- **OCR Quality**: 1-3 seconds for assessment
- **Network dependent**: Times may vary based on connection and API load

## Security Notes
- Test environment uses mock configurations
- No real SSM parameters are accessed
- API keys should be kept secure
- Results may contain sensitive document data

## Next Steps
After local testing, you can:
1. Deploy fixes to staging environment
2. Run production tests with real data
3. Monitor performance metrics
4. Compare consistency between providers
