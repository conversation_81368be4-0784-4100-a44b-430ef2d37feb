#!/usr/bin/env python3
"""
Quick Test Script for LLM Providers
===================================
Simple script to quickly test Gemini and Bedrock with the PDF in your repo.
"""

import os
import sys
import json
import time

# Set test environment
os.environ['ENV'] = 'test'

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.services.process import process_scanned_pdf
from core.utils.utils import load_pdf_with_text
from core.logger.logger import get_logger, log_frame_info, log_frame_error
from core.enums.enums import ResponseStatus
from config.config import config

logger = get_logger(__name__)

def test_provider(provider_name: str, pdf_path: str = "tenancy-contract-01-bad.pdf"):
    """Test a single provider with the PDF."""
    print(f"\n🚀 Testing {provider_name}...")
    
    try:
        # Set provider
        original_provider = config.get("llm_provider")
        config.ssm_config["llm_provider"] = provider_name.lower()
        
        # Read PDF
        with open(pdf_path, 'rb') as f:
            pdf_bytes = f.read()
        
        # Sample claims payload - format expected by process_scanned_pdf
        claim_payload = {
            "payload": {
                "policy_number": "",
                "insured_name": "",
                "claim_number": "",
                "incident_date": "",
                "claim_amount": "",
                "property_address": ""
            }
        }
        
        # Check if text-based or scanned
        page_contents = load_pdf_with_text(pdf_bytes)
        if page_contents:
            print(f"📄 Processing as text-based PDF")
            from core.services.process import process_text_pdf
            # For text PDF, we need business_rules format
            text_payload = {
                "business_rules": list(claim_payload["payload"].keys())
            }
            result = process_text_pdf(text_payload, page_contents)
        else:
            print(f"🖼️ Processing as scanned PDF")
            request_id = f"quick_test_{provider_name.lower()}_{int(time.time())}"
            result = process_scanned_pdf(request_id, pdf_bytes, claim_payload, retry=False)
        
        # Restore provider
        config.ssm_config["llm_provider"] = original_provider
        
        # Print results
        if result.get("status") == ResponseStatus.SUCCESS.value:
            print(f"✅ {provider_name} SUCCESS")
            extracted = result.get("extracted_claims", {})
            print(f"📋 Extracted {len(extracted)} fields:")
            for field, value in extracted.items():
                print(f"  • {field}: {value}")
            
            if "llm_usage" in result:
                usage = result["llm_usage"]
                print(f"📊 Tokens: {usage.get('inputTokens', 0)} in, {usage.get('outputTokens', 0)} out")
        else:
            print(f"❌ {provider_name} FAILED: {result.get('message', 'Unknown error')}")
        
        return result
        
    except Exception as e:
        print(f"❌ {provider_name} ERROR: {str(e)}")
        config.ssm_config["llm_provider"] = original_provider
        return {"status": "FAILED", "error": str(e)}

def main():
    """Quick test both providers."""
    print("🧪 Quick LLM Provider Test")
    print("=" * 40)
    
    # Check PDF exists
    pdf_path = "tenancy-contract-01-bad.pdf"
    if not os.path.exists(pdf_path):
        print(f"❌ PDF not found: {pdf_path}")
        return
    
    print(f"✅ Found PDF: {pdf_path}")
    
    # Test both providers
    bedrock_result = test_provider("Bedrock")
    gemini_result = test_provider("Gemini")
    
    # Quick comparison
    print(f"\n{'='*40}")
    print("📊 COMPARISON SUMMARY")
    print(f"{'='*40}")
    
    bedrock_success = bedrock_result.get("status") == ResponseStatus.SUCCESS.value
    gemini_success = gemini_result.get("status") == ResponseStatus.SUCCESS.value
    
    print(f"Bedrock: {'✅ SUCCESS' if bedrock_success else '❌ FAILED'}")
    print(f"Gemini:  {'✅ SUCCESS' if gemini_success else '❌ FAILED'}")
    
    if bedrock_success and gemini_success:
        bedrock_claims = bedrock_result.get("extracted_claims", {})
        gemini_claims = gemini_result.get("extracted_claims", {})
        
        # Quick field comparison
        all_fields = set(bedrock_claims.keys()) | set(gemini_claims.keys())
        matches = 0
        differences = 0
        
        for field in all_fields:
            bedrock_val = bedrock_claims.get(field, "NOT_FOUND")
            gemini_val = gemini_claims.get(field, "NOT_FOUND")
            if bedrock_val == gemini_val:
                matches += 1
            else:
                differences += 1
        
        print(f"Field Matches: {matches}")
        print(f"Differences: {differences}")
        print(f"Consistency: {'✅ HIGH' if differences <= 1 else '⚠️ LOW'}")
    
    print(f"\n🎉 Test completed!")

if __name__ == "__main__":
    main()
