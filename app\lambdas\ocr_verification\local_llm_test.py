#!/usr/bin/env python3
"""
Local LLM Test Script
====================
Interactive script to test Gemini and Bedrock LLM providers locally with PDF documents.
Allows you to choose provider, test type, and see detailed results.
"""

import os
import sys
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional

# Set test environment to avoid SSM issues
os.environ['ENV'] = 'test'

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.services.process import process_text_pdf, process_scanned_pdf, extract_parameters_from_image
from core.services.lambda_ocr_service import assess_pdf_quality_with_lambda_ocr
from core.llm.llm_factory import LlmFactory
from core.utils.utils import load_pdf_with_text, convert_pdf_to_images
from core.logger.logger import get_logger, log_frame_info, log_frame_error, log_frame_warning
from core.enums.enums import ResponseStatus
from config.config import config

logger = get_logger(__name__)

class LocalLLMTester:
    """Interactive local LLM testing utility."""
    
    def __init__(self):
        """Initialize the tester."""
        self.bedrock_provider = None
        self.gemini_provider = None
        self.pdf_path = "tenancy-contract-01-bad.pdf"
        self.last_results = None
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize both LLM providers."""
        try:
            LlmFactory.clear_cache()
            
            # Initialize Bedrock
            from core.llm.bedrock import BedrockLlm
            self.bedrock_provider = BedrockLlm
            log_frame_info(logger, "✅ Bedrock LLM provider initialized")
            
            # Initialize Gemini
            from core.llm.gemini import GeminiLlm
            self.gemini_provider = GeminiLlm
            log_frame_info(logger, "✅ Gemini LLM provider initialized")
            
        except Exception as e:
            log_frame_error(logger, f"Failed to initialize LLM providers: {e}")
            raise
    
    def check_pdf_exists(self) -> bool:
        """Check if the test PDF exists."""
        if os.path.exists(self.pdf_path):
            log_frame_info(logger, f"✅ Found test PDF: {self.pdf_path}")
            return True
        else:
            log_frame_error(logger, f"❌ Test PDF not found: {self.pdf_path}")
            return False
    
    def analyze_pdf_structure(self) -> Dict[str, Any]:
        """Analyze the PDF structure to determine processing approach."""
        log_frame_info(logger, "🔍 Analyzing PDF structure...")
        
        try:
            # Check if PDF has extractable text
            with open(self.pdf_path, 'rb') as f:
                pdf_bytes = f.read()
            
            page_contents = load_pdf_with_text(pdf_bytes)
            
            if page_contents:
                log_frame_info(logger, f"📄 PDF has extractable text ({len(page_contents)} pages)")
                return {
                    "type": "text_based",
                    "pages": len(page_contents),
                    "content_preview": page_contents[0][:200] + "..." if page_contents[0] else "No content"
                }
            else:
                log_frame_info(logger, "🖼️ PDF appears to be scanned (no extractable text)")
                
                # Convert to images for analysis
                images = convert_pdf_to_images(pdf_bytes)
                image_info = []
                for i, img in enumerate(images[:3]):  # First 3 images
                    if hasattr(img, 'size'):
                        image_info.append(f"{img.size[0]}x{img.size[1]}")
                    else:
                        image_info.append(f"Image {i+1}")

                return {
                    "type": "scanned",
                    "pages": len(images),
                    "image_sizes": image_info
                }
                
        except Exception as e:
            log_frame_error(logger, f"Error analyzing PDF: {e}")
            return {"type": "unknown", "error": str(e)}
    
    def test_single_provider(self, provider_name: str, test_type: str = "auto") -> Dict[str, Any]:
        """Test a single LLM provider."""
        log_frame_info(logger, f"🚀 Testing {provider_name} provider...")
        
        start_time = time.time()
        
        try:
            # Set the LLM provider
            original_provider = config.get("llm_provider")
            config.ssm_config["llm_provider"] = provider_name.lower()
            
            # Read PDF
            with open(self.pdf_path, 'rb') as f:
                pdf_bytes = f.read()
            
            # Sample claims payload - format for scanned PDF processing
            claim_payload = {
                "payload": {
                    "policy_number": "",
                    "insured_name": "",
                    "claim_number": "",
                    "incident_date": "",
                    "claim_amount": "",
                    "property_address": "",
                    "damage_description": ""
                }
            }
            
            # Determine processing approach
            if test_type == "auto":
                page_contents = load_pdf_with_text(pdf_bytes)
                if page_contents:
                    test_type = "text"
                else:
                    test_type = "scanned"
            
            # Process based on type
            if test_type == "text":
                log_frame_info(logger, f"📄 Processing as text-based PDF with {provider_name}")
                page_contents = load_pdf_with_text(pdf_bytes)
                # For text PDF, convert to business_rules format
                text_payload = {
                    "business_rules": list(claim_payload["payload"].keys())
                }
                result = process_text_pdf(text_payload, page_contents)
            else:
                log_frame_info(logger, f"🖼️ Processing as scanned PDF with {provider_name}")
                request_id = f"local_test_{provider_name.lower()}_{int(time.time())}"
                result = process_scanned_pdf(request_id, pdf_bytes, claim_payload, retry=False)
            
            processing_time = time.time() - start_time
            result["processing_time"] = processing_time
            result["provider"] = provider_name
            result["test_type"] = test_type
            
            # Restore original provider
            config.ssm_config["llm_provider"] = original_provider
            
            log_frame_info(logger, f"✅ {provider_name} processing completed in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            log_frame_error(logger, f"❌ {provider_name} processing failed: {e}")
            
            # Restore original provider
            config.ssm_config["llm_provider"] = original_provider
            
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"{provider_name} processing failed: {str(e)}",
                "provider": provider_name,
                "processing_time": processing_time,
                "test_type": test_type
            }
    
    def compare_providers(self, bedrock_result: Dict[str, Any], gemini_result: Dict[str, Any]) -> Dict[str, Any]:
        """Compare results from both providers."""
        log_frame_info(logger, "🔍 Comparing provider results...")
        
        comparison = {
            "bedrock_success": bedrock_result.get("status") == ResponseStatus.SUCCESS.value,
            "gemini_success": gemini_result.get("status") == ResponseStatus.SUCCESS.value,
            "bedrock_time": bedrock_result.get("processing_time", 0),
            "gemini_time": gemini_result.get("processing_time", 0),
            "differences": [],
            "similarities": []
        }
        
        # Compare extracted claims if both succeeded
        if comparison["bedrock_success"] and comparison["gemini_success"]:
            bedrock_claims = bedrock_result.get("extracted_claims", {})
            gemini_claims = gemini_result.get("extracted_claims", {})
            
            all_fields = set(bedrock_claims.keys()) | set(gemini_claims.keys())
            
            for field in all_fields:
                bedrock_value = bedrock_claims.get(field, "NOT_FOUND")
                gemini_value = gemini_claims.get(field, "NOT_FOUND")
                
                if bedrock_value == gemini_value:
                    comparison["similarities"].append({
                        "field": field,
                        "value": bedrock_value
                    })
                else:
                    comparison["differences"].append({
                        "field": field,
                        "bedrock": bedrock_value,
                        "gemini": gemini_value
                    })
        
        comparison["consistency"] = len(comparison["differences"]) == 0
        return comparison
    
    def print_results(self, result: Dict[str, Any], provider_name: str):
        """Print formatted results."""
        print(f"\n{'='*60}")
        print(f"🤖 {provider_name.upper()} RESULTS")
        print(f"{'='*60}")
        
        print(f"Status: {result.get('status', 'UNKNOWN')}")
        print(f"Processing Time: {result.get('processing_time', 0):.2f}s")
        print(f"Test Type: {result.get('test_type', 'unknown')}")
        
        if result.get("status") == ResponseStatus.SUCCESS.value:
            extracted_claims = result.get("extracted_claims", {})
            print(f"\n📋 Extracted Claims ({len(extracted_claims)} fields):")
            for field, value in extracted_claims.items():
                print(f"  • {field}: {value}")
                
            if "llm_usage" in result:
                usage = result["llm_usage"]
                print(f"\n📊 Token Usage:")
                print(f"  • Input: {usage.get('inputTokens', 0)}")
                print(f"  • Output: {usage.get('outputTokens', 0)}")
                print(f"  • Total: {usage.get('totalTokens', 0)}")
        else:
            print(f"\n❌ Error: {result.get('message', 'Unknown error')}")
    
    def print_comparison(self, comparison: Dict[str, Any]):
        """Print comparison results."""
        print(f"\n{'='*60}")
        print("🔍 PROVIDER COMPARISON")
        print(f"{'='*60}")
        
        print(f"Bedrock Success: {'✅' if comparison['bedrock_success'] else '❌'}")
        print(f"Gemini Success: {'✅' if comparison['gemini_success'] else '❌'}")
        print(f"Results Consistent: {'✅' if comparison['consistency'] else '❌'}")
        print(f"Bedrock Time: {comparison['bedrock_time']:.2f}s")
        print(f"Gemini Time: {comparison['gemini_time']:.2f}s")
        
        if comparison["similarities"]:
            print(f"\n✅ Matching Fields ({len(comparison['similarities'])}):")
            for sim in comparison["similarities"]:
                print(f"  • {sim['field']}: {sim['value']}")
        
        if comparison["differences"]:
            print(f"\n⚠️ Different Fields ({len(comparison['differences'])}):")
            for diff in comparison["differences"]:
                print(f"  • {diff['field']}:")
                print(f"    - Bedrock: {diff['bedrock']}")
                print(f"    - Gemini: {diff['gemini']}")

    def save_results_to_file(self, results: Dict[str, Any], filename: str):
        """Save test results to JSON file."""
        try:
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            log_frame_info(logger, f"✅ Results saved to {filename}")
        except Exception as e:
            log_frame_error(logger, f"Failed to save results: {e}")

    def test_ocr_quality(self) -> Dict[str, Any]:
        """Test OCR quality assessment."""
        log_frame_info(logger, "🔍 Testing OCR quality assessment...")

        try:
            with open(self.pdf_path, 'rb') as f:
                pdf_bytes = f.read()

            quality_result = assess_pdf_quality_with_lambda_ocr(pdf_bytes)
            return quality_result

        except Exception as e:
            log_frame_error(logger, f"OCR quality test failed: {e}")
            return {"status": "FAILED", "error": str(e)}

def main():
    """Main interactive function."""
    print("🚀 Local LLM Testing Script")
    print("=" * 50)

    tester = LocalLLMTester()

    # Check PDF exists
    if not tester.check_pdf_exists():
        print("❌ Cannot proceed without test PDF")
        return

    # Analyze PDF
    pdf_analysis = tester.analyze_pdf_structure()
    print(f"\n📄 PDF Analysis:")
    print(f"  Type: {pdf_analysis.get('type', 'unknown')}")
    print(f"  Pages: {pdf_analysis.get('pages', 'unknown')}")
    if pdf_analysis.get('type') == 'text_based':
        print(f"  Content Preview: {pdf_analysis.get('content_preview', 'N/A')}")
    elif pdf_analysis.get('type') == 'scanned':
        print(f"  Image Sizes: {pdf_analysis.get('image_sizes', 'N/A')}")

    while True:
        print(f"\n{'='*50}")
        print("Choose test option:")
        print("1. Test Bedrock only")
        print("2. Test Gemini only")
        print("3. Test both providers (comparison)")
        print("4. Test OCR quality assessment")
        print("5. Custom business rules test")
        print("6. Save last results to file")
        print("7. Exit")

        choice = input("\nEnter choice (1-7): ").strip()

        if choice == "1":
            result = tester.test_single_provider("Bedrock")
            tester.print_results(result, "Bedrock")
            tester.last_results = {"bedrock": result}

        elif choice == "2":
            result = tester.test_single_provider("Gemini")
            tester.print_results(result, "Gemini")
            tester.last_results = {"gemini": result}

        elif choice == "3":
            print("\n🔄 Testing both providers...")
            bedrock_result = tester.test_single_provider("Bedrock")
            gemini_result = tester.test_single_provider("Gemini")

            tester.print_results(bedrock_result, "Bedrock")
            tester.print_results(gemini_result, "Gemini")

            comparison = tester.compare_providers(bedrock_result, gemini_result)
            tester.print_comparison(comparison)

            tester.last_results = {
                "bedrock": bedrock_result,
                "gemini": gemini_result,
                "comparison": comparison
            }

        elif choice == "4":
            print("\n🔍 Testing OCR quality assessment...")
            quality_result = tester.test_ocr_quality()
            print(f"\n📊 OCR Quality Results:")
            print(json.dumps(quality_result, indent=2))
            tester.last_results = {"ocr_quality": quality_result}

        elif choice == "5":
            print("\n📝 Custom Business Rules Test")
            print("Enter business rules (comma-separated):")
            print("Example: policy_number,claim_amount,insured_name")
            custom_rules = input("Rules: ").strip().split(',')
            custom_rules = [rule.strip() for rule in custom_rules if rule.strip()]

            if custom_rules:
                print(f"Testing with rules: {custom_rules}")
                # Update the claim payload in test_single_provider would need modification
                print("Note: This would require modifying the test method to accept custom rules")
            else:
                print("❌ No valid rules provided")

        elif choice == "6":
            if hasattr(tester, 'last_results'):
                filename = f"local_test_results_{int(time.time())}.json"
                tester.save_results_to_file(tester.last_results, filename)
            else:
                print("❌ No results to save. Run a test first.")

        elif choice == "7":
            print("👋 Goodbye!")
            break

        else:
            print("❌ Invalid choice. Please enter 1-7.")

if __name__ == "__main__":
    main()
