#!/usr/bin/env python3
"""
Simple LLM Test Script
=====================
Direct test of LLM providers without S3 dependencies.
Tests the core LLM functionality with image processing.
"""

import os
import sys
import json
import time
import base64
from io import BytesIO

# Set test environment
os.environ['ENV'] = 'test'

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.utils.utils import load_pdf_with_text, convert_pdf_to_images
from core.llm.llm_factory import LlmFactory
from core.logger.logger import get_logger, log_frame_info, log_frame_error
from config.config import config

logger = get_logger(__name__)

def convert_image_to_base64(image):
    """Convert PIL image to base64 string."""
    buffered = BytesIO()
    image.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    return img_str

def test_llm_directly(provider_name: str, images: list, business_rules: list):
    """Test LLM provider directly with images."""
    print(f"\n🚀 Testing {provider_name} directly...")
    
    try:
        # Set provider
        original_provider = config.get("llm_provider")
        config.ssm_config["llm_provider"] = provider_name.lower()
        
        # Get LLM instance
        llm_instance = LlmFactory.get_llm_provider()
        
        # Prepare prompt
        prompt = f"""
You are an expert document analyzer. Extract the following information from this insurance document:

Business Rules to Extract:
{', '.join(business_rules)}

For each field, provide the exact value found in the document. If a field is not found, return "NOT_FOUND".

Return your response as a JSON object with the field names as keys and extracted values as values.

Example format:
{{
    "policy_number": "POL-2024-001",
    "insured_name": "John Smith",
    "claim_number": "CLM-2024-456"
}}
"""
        
        # Convert first image to bytes for testing
        if images:
            # images is a list of tuples (page_number, PIL.Image)
            first_image = images[0][1]  # Get the PIL.Image from the first tuple

            # Convert PIL image to bytes
            buffered = BytesIO()
            first_image.save(buffered, format="JPEG")
            image_bytes = buffered.getvalue()

            # Create message with image
            message_content = [
                {"text": prompt},
                {"image": {"format": "jpeg", "source": {"bytes": image_bytes}}}
            ]
            message_list = [{"role": "user", "content": message_content}]

            # Test the provider
            if hasattr(llm_instance, 'converse_model'):
                # Check if provider supports business_rules parameter (Gemini)
                if 'business_rules' in llm_instance.converse_model.__code__.co_varnames:
                    result = llm_instance.converse_model(messages=message_list, business_rules=business_rules)
                else:
                    result = llm_instance.converse_model(messages=message_list)
            else:
                raise ValueError(f"Provider {provider_name} doesn't have converse_model method")
            
            # Restore provider
            config.ssm_config["llm_provider"] = original_provider
            
            print(f"✅ {provider_name} responded successfully")
            
            # Try to parse JSON response
            try:
                if isinstance(result, dict):
                    # Check for different response formats
                    if 'result' in result:
                        content = result['result']
                    elif 'content' in result:
                        content = result['content']
                    else:
                        content = str(result)
                elif hasattr(result, 'content'):
                    content = result.content
                else:
                    content = str(result)
                
                # Extract JSON from content
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    extracted_data = json.loads(json_match.group())
                    print(f"📋 Extracted {len(extracted_data)} fields:")
                    for field, value in extracted_data.items():
                        print(f"  • {field}: {value}")
                    return {
                        "status": "SUCCESS",
                        "extracted_claims": extracted_data,
                        "raw_response": content
                    }
                else:
                    print(f"⚠️ Could not parse JSON from response")
                    print(f"Raw response: {content[:200]}...")
                    return {
                        "status": "PARTIAL_SUCCESS",
                        "raw_response": content
                    }
                    
            except json.JSONDecodeError as e:
                print(f"⚠️ JSON parsing failed: {e}")
                print(f"Raw response: {content[:200]}...")
                return {
                    "status": "PARTIAL_SUCCESS",
                    "raw_response": content
                }
        else:
            raise ValueError("No images provided")
            
    except Exception as e:
        print(f"❌ {provider_name} failed: {str(e)}")
        config.ssm_config["llm_provider"] = original_provider
        return {
            "status": "FAILED",
            "error": str(e)
        }

def main():
    """Main test function."""
    print("🧪 Simple LLM Provider Test")
    print("=" * 40)
    
    # Check PDF exists
    pdf_path = "tenancy-contract-01-bad.pdf"
    if not os.path.exists(pdf_path):
        print(f"❌ PDF not found: {pdf_path}")
        return
    
    print(f"✅ Found PDF: {pdf_path}")
    
    try:
        # Read and convert PDF
        with open(pdf_path, 'rb') as f:
            pdf_bytes = f.read()
        
        # Check if text-based first
        page_contents = load_pdf_with_text(pdf_bytes)
        if page_contents:
            print("📄 PDF has extractable text - this test focuses on image processing")
            print("Converting to images anyway for LLM testing...")
        
        # Convert to images
        print("🖼️ Converting PDF to images...")
        images = convert_pdf_to_images(pdf_bytes)
        print(f"✅ Converted to {len(images)} images")
        
        # Business rules to test
        business_rules = [
            "policy_number",
            "insured_name", 
            "claim_number",
            "incident_date",
            "claim_amount",
            "property_address"
        ]
        
        print(f"📋 Testing extraction of: {', '.join(business_rules)}")
        
        # Test both providers
        bedrock_result = test_llm_directly("Bedrock", images, business_rules)
        gemini_result = test_llm_directly("Gemini", images, business_rules)
        
        # Summary
        print(f"\n{'='*40}")
        print("📊 TEST SUMMARY")
        print(f"{'='*40}")
        
        bedrock_success = bedrock_result.get("status") == "SUCCESS"
        gemini_success = gemini_result.get("status") == "SUCCESS"
        
        print(f"Bedrock: {'✅ SUCCESS' if bedrock_success else '❌ FAILED'}")
        print(f"Gemini:  {'✅ SUCCESS' if gemini_success else '❌ FAILED'}")
        
        if bedrock_success and gemini_success:
            # Compare results
            bedrock_claims = bedrock_result.get("extracted_claims", {})
            gemini_claims = gemini_result.get("extracted_claims", {})
            
            print(f"\n🔍 COMPARISON:")
            all_fields = set(bedrock_claims.keys()) | set(gemini_claims.keys())
            matches = 0
            differences = 0
            
            for field in all_fields:
                bedrock_val = bedrock_claims.get(field, "NOT_FOUND")
                gemini_val = gemini_claims.get(field, "NOT_FOUND")
                if bedrock_val == gemini_val:
                    matches += 1
                    print(f"  ✅ {field}: {bedrock_val}")
                else:
                    differences += 1
                    print(f"  ⚠️ {field}:")
                    print(f"    - Bedrock: {bedrock_val}")
                    print(f"    - Gemini: {gemini_val}")
            
            print(f"\nMatches: {matches}, Differences: {differences}")
            consistency = "HIGH" if differences <= 1 else "MEDIUM" if differences <= 3 else "LOW"
            print(f"Consistency: {consistency}")
        
        # Save results
        results = {
            "timestamp": time.time(),
            "bedrock": bedrock_result,
            "gemini": gemini_result,
            "pdf_info": {
                "path": pdf_path,
                "pages": len(images),
                "has_text": bool(page_contents)
            }
        }
        
        filename = f"simple_test_results_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n💾 Results saved to: {filename}")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
